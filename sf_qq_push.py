#!/usr/bin/env python3
# _*_ coding:utf-8 _*_

"""
顺丰速运QQ推送脚本
配合顺丰非插件版.js使用，发送运行结果到QQ群
"""

import os
import sys
import requests

def napcat_push(title: str, content: str) -> None:
    """napcat 推送函数"""
    napcat_url = os.getenv("http://192.168.10.252:7765/send_msg")
    napcat_group_id = os.getenv("1027740020")
    
    if not napcat_url or not napcat_group_id:
        print("napcat 服务的 NAPCAT_URL 或 NAPCAT_GROUP_ID 未设置!!")
        return
    
    print("napcat 服务启动")
    
    try:
        if napcat_url.endswith('/send_group_msg') or napcat_url.endswith('/send_msg'):
            url = napcat_url
        else:
            url = f'{napcat_url}/send_group_msg'
        
        group_id = int(napcat_group_id)
        message = f"{title}\n\n{content}"
        
        response = requests.post(
            url,
            json={"group_id": group_id, "message": message},
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        
        if response.status_code == 200:
            print("napcat QQ群推送成功！")
        else:
            print(f"napcat 推送失败，状态码：{response.status_code}，响应：{response.text}")
    except Exception as e:
        print(f"napcat 推送异常：{str(e)}")

def main():
    """主函数"""
    print("=== 顺丰速运QQ推送 ===")
    
    # 检查命令行参数
    if len(sys.argv) >= 3:
        title = sys.argv[1]
        content = sys.argv[2]
    else:
        # 使用默认内容
        title = "顺丰速运"
        content = """脚本运行完成！

详细运行结果请查看：
1. 青龙面板日志
2. 邮箱通知

如有问题请检查青龙面板任务状态。"""
    
    print("============== 推送 ==============")
    napcat_push(title, content)

if __name__ == "__main__":
    main()
