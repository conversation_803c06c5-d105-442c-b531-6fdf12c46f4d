#!/usr/bin/env python3
# _*_ coding:utf-8 _*_

"""
顺丰速运QQ推送脚本
配合顺丰非插件版.js使用，发送运行结果到QQ群
"""

import os
import sys
import requests
import time

import subprocess

def napcat_push(title: str, content: str) -> None:
    """napcat 推送函数"""
    # 直接使用固定值，不使用环境变量
    napcat_url = "http://192.168.10.252:7765/send_msg"
    napcat_group_id = "1027740020"
    
    if not napcat_url or not napcat_group_id:
        print("napcat 服务的 NAPCAT_URL 或 NAPCAT_GROUP_ID 未设置!!")
        return
    
    print("napcat 服务启动")
    
    try:
        if napcat_url.endswith('/send_group_msg') or napcat_url.endswith('/send_msg'):
            url = napcat_url
        else:
            url = f'{napcat_url}/send_group_msg'
        
        group_id = int(napcat_group_id)
        message = f"{title}\n\n{content}"
        
        response = requests.post(
            url,
            json={"group_id": group_id, "message": message},
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        
        if response.status_code == 200:
            print("napcat QQ群推送成功！")
        else:
            print(f"napcat 推送失败，状态码：{response.status_code}，响应：{response.text}")
    except Exception as e:
        print(f"napcat 推送异常：{str(e)}")

def get_latest_sf_log():
    """获取最新的顺丰速运日志"""
    try:
        import glob
        import datetime

        # 直接查找顺丰非插件版_4目录
        target_dirs = [
            "data/log/顺丰非插件版_4",
            "/ql/data/log/顺丰非插件版_4",
            "./data/log/顺丰非插件版_4",
            "../data/log/顺丰非插件版_4",
            "../../data/log/顺丰非插件版_4"
        ]

        print(f"[DEBUG] 当前工作目录: {os.getcwd()}")
        print(f"[DEBUG] 查找顺丰非插件版_4目录...")

        for target_dir in target_dirs:
            print(f"[DEBUG] 检查目录: {target_dir}")
            if os.path.exists(target_dir) and os.path.isdir(target_dir):
                print(f"[DEBUG] 找到目录: {target_dir}")

                # 列出目录中的所有文件
                all_files = glob.glob(f"{target_dir}/*")
                print(f"[DEBUG] 目录中的文件: {all_files}")

                # 查找今天的日志文件
                today = datetime.datetime.now().strftime("%Y-%m-%d")

                # 查找各种可能的日志文件
                log_files = []
                patterns = [
                    f"{target_dir}/*{today}*",
                    f"{target_dir}/*.log",
                    f"{target_dir}/*"
                ]

                for pattern in patterns:
                    found_files = glob.glob(pattern)
                    log_files.extend([f for f in found_files if os.path.isfile(f)])

                if log_files:
                    # 获取最新的日志文件
                    latest_log = max(log_files, key=os.path.getmtime)
                    print(f"[DEBUG] 使用日志文件: {latest_log}")

                    # 读取日志内容
                    with open(latest_log, 'r', encoding='utf-8', errors='ignore') as f:
                        content = f.read()

                    # 过滤并提取有用信息
                    if content.strip():
                        lines = content.split('\n')
                        result_lines = []

                        # 跳过广告行和无关内容
                        skip_patterns = [
                            '=>=>=>=>____来自',
                            '## 开始执行',
                            '## 执行结束',
                            '耗时',
                            '秒　　　　　'
                        ]

                        # 提取有用的行
                        for line in lines:
                            line = line.strip()
                            if not line:
                                continue

                            # 跳过广告和无关内容
                            if any(pattern in line for pattern in skip_patterns):
                                continue

                            # 保留有用的信息
                            if any(keyword in line for keyword in [
                                '[顺丰速运]开始运行',
                                '共找到',
                                '采蜜游戏目前设置',
                                '账号[',
                                '登录成功',
                                '签到',
                                '积分:',
                                '采蜜',
                                '丰蜜:',
                                '运行结束',
                                'SMTP 发送通知消息成功'
                            ]):
                                # 清理时间戳
                                clean_line = line
                                if clean_line.startswith('[') and ']' in clean_line:
                                    # 移除时间戳 [17:16:23]
                                    clean_line = clean_line.split(']', 1)[-1].strip()
                                result_lines.append(clean_line)

                        if result_lines:
                            return '\n'.join(result_lines)
                        else:
                            return content

                break

        print("[DEBUG] 未找到顺丰非插件版_4目录或日志文件")
        return None
    except Exception as e:
        print(f"读取日志失败: {e}")
        return None

def main():
    """主函数"""
    print("=== 顺丰速运QQ推送 ===")

    # 检查命令行参数
    if len(sys.argv) >= 3:
        title = sys.argv[1]
        content = sys.argv[2]
        print("使用命令行参数内容")
    else:
        # 尝试获取真实的顺丰日志内容
        title = "顺丰速运"
        log_content = get_latest_sf_log()

        if log_content:
            content = log_content
            print("使用最新日志内容")
        else:
            # 如果无法获取日志，使用通用提示
            content = """脚本运行完成！

详细运行结果请查看：
1. 青龙面板日志
2. 邮箱通知

如有问题请检查青龙面板任务状态。"""
            print("使用默认提示内容")

    print("============== 推送 ==============")
    napcat_push(title, content)

if __name__ == "__main__":
    main()
