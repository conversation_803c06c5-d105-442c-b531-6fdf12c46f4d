#!/usr/bin/env python3
# _*_ coding:utf-8 _*_

"""
顺丰速运QQ推送脚本
配合顺丰非插件版.js使用，发送运行结果到QQ群
"""

import os
import sys
import requests
import time
import re
import subprocess

def napcat_push(title: str, content: str) -> None:
    """napcat 推送函数"""
    # 直接使用固定值，不使用环境变量
    napcat_url = "http://192.168.10.252:7765/send_msg"
    napcat_group_id = "1027740020"
    
    if not napcat_url or not napcat_group_id:
        print("napcat 服务的 NAPCAT_URL 或 NAPCAT_GROUP_ID 未设置!!")
        return
    
    print("napcat 服务启动")
    
    try:
        if napcat_url.endswith('/send_group_msg') or napcat_url.endswith('/send_msg'):
            url = napcat_url
        else:
            url = f'{napcat_url}/send_group_msg'
        
        group_id = int(napcat_group_id)
        message = f"{title}\n\n{content}"
        
        response = requests.post(
            url,
            json={"group_id": group_id, "message": message},
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        
        if response.status_code == 200:
            print("napcat QQ群推送成功！")
        else:
            print(f"napcat 推送失败，状态码：{response.status_code}，响应：{response.text}")
    except Exception as e:
        print(f"napcat 推送异常：{str(e)}")

def get_latest_sf_log():
    """获取最新的顺丰速运日志"""
    try:
        # 尝试读取青龙面板日志目录
        log_paths = [
            "/ql/data/log",
            "/ql/log",
            "./log",
            "../log"
        ]

        for log_path in log_paths:
            if os.path.exists(log_path):
                # 查找最新的顺丰相关日志文件
                import glob
                sf_logs = glob.glob(f"{log_path}/*顺丰*")
                if sf_logs:
                    # 获取最新的日志文件
                    latest_log = max(sf_logs, key=os.path.getmtime)

                    # 读取日志内容
                    with open(latest_log, 'r', encoding='utf-8') as f:
                        content = f.read()

                    # 提取有用信息
                    lines = content.split('\n')
                    result_lines = []

                    for line in lines:
                        if any(keyword in line for keyword in [
                            '账号[', '登录成功', '签到', '积分', '采蜜', '丰蜜'
                        ]):
                            result_lines.append(line.strip())

                    if result_lines:
                        return '\n'.join(result_lines[-20:])  # 返回最后20行相关内容
                break

        return None
    except Exception as e:
        print(f"读取日志失败: {e}")
        return None

def main():
    """主函数"""
    print("=== 顺丰速运QQ推送 ===")
    
    # 检查命令行参数
    if len(sys.argv) >= 3:
        title = sys.argv[1]
        content = sys.argv[2]
    else:
        # 使用默认内容
        title = "顺丰速运"
        content = """脚本运行完成！

详细运行结果请查看：
1. 青龙面板日志
2. 邮箱通知

如有问题请检查青龙面板任务状态。"""
    
    print("============== 推送 ==============")
    napcat_push(title, content)

if __name__ == "__main__":
    main()
