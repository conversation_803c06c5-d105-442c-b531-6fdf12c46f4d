#!/usr/bin/env python3
# _*_ coding:utf-8 _*_

import os
import sys

print("=== 检查实际运行环境的环境变量 ===")

# 检查所有相关的环境变量
env_vars = [
    "NAPCAT_URL",
    "NAPCAT_GROUP_ID", 
    "SMTP_SERVER",
    "SMTP_EMAIL",
    "SMTP_PASSWORD",
    "SMTP_NAME",
    "SMTP_SSL"
]

for var in env_vars:
    value = os.getenv(var)
    if value:
        # 对于密码类的敏感信息，只显示前几位
        if "PASSWORD" in var.upper():
            display_value = value[:3] + "***" if len(value) > 3 else "***"
        else:
            display_value = value
        print(f"{var}: {display_value}")
    else:
        print(f"{var}: 未设置")

# 导入notify模块并检查配置
print("\n=== 导入notify模块并检查配置 ===")
try:
    import importlib.util
    
    # 优先尝试notify(1).py，因为顺丰速运脚本可能使用这个
    notify_files = ["notify(1).py", "notify.py"]
    notify_module = None
    
    for notify_file in notify_files:
        if os.path.exists(notify_file):
            try:
                spec = importlib.util.spec_from_file_location("notify", notify_file)
                notify_module = importlib.util.module_from_spec(spec)
                spec.loader.exec_module(notify_module)
                print(f"成功导入: {notify_file}")
                break
            except Exception as e:
                print(f"导入 {notify_file} 失败: {e}")
                continue
    
    if not notify_module:
        print("无法导入任何notify模块")
        sys.exit(1)
    
    # 检查push_config
    print(f"\nnotify模块中的配置:")
    print(f"NAPCAT_URL: {notify_module.push_config.get('NAPCAT_URL')}")
    print(f"NAPCAT_GROUP_ID: {notify_module.push_config.get('NAPCAT_GROUP_ID')}")
    
    # 检查推送函数列表
    notify_functions = notify_module.add_notify_function()
    function_names = [func.__name__ for func in notify_functions]
    print(f"\n已注册的推送函数: {function_names}")
    print(f"napcat 是否启用: {'napcat' in function_names}")
    
    # 如果napcat启用了，测试一下
    if 'napcat' in function_names:
        print("\n=== 测试napcat推送 ===")
        try:
            notify_module.napcat("环境测试", "这是一条环境测试消息")
        except Exception as e:
            print(f"napcat测试失败: {e}")
    else:
        print("\n=== napcat未启用，检查原因 ===")
        napcat_url = notify_module.push_config.get("NAPCAT_URL")
        napcat_group_id = notify_module.push_config.get("NAPCAT_GROUP_ID")
        print(f"NAPCAT_URL 存在: {bool(napcat_url)}")
        print(f"NAPCAT_GROUP_ID 存在: {bool(napcat_group_id)}")

except Exception as e:
    print(f"检查过程中出错: {e}")

print("\n=== 检查完成 ===")
