#!/usr/bin/env python3
# _*_ coding:utf-8 _*_

import os
import sys
import importlib.util

# 导入notify模块
spec = importlib.util.spec_from_file_location("notify", "notify(1).py")
notify = importlib.util.module_from_spec(spec)
spec.loader.exec_module(notify)

def debug_config():
    """调试配置信息"""
    print("=== 环境变量调试 ===")
    
    # 检查环境变量
    napcat_url = os.getenv("NAPCAT_URL")
    napcat_group_id = os.getenv("NAPCAT_GROUP_ID")
    
    print(f"环境变量 NAPCAT_URL: {napcat_url}")
    print(f"环境变量 NAPCAT_GROUP_ID: {napcat_group_id}")
    
    # 检查push_config中的值
    print(f"push_config NAPCAT_URL: {notify.push_config.get('NAPCAT_URL')}")
    print(f"push_config NAPCAT_GROUP_ID: {notify.push_config.get('NAPCAT_GROUP_ID')}")
    
    # 检查推送函数列表
    print("\n=== 推送函数列表 ===")
    notify_functions = notify.add_notify_function()
    print(f"已注册的推送函数数量: {len(notify_functions)}")
    
    for func in notify_functions:
        print(f"- {func.__name__}")
    
    # 检查napcat是否在列表中
    napcat_enabled = any(func.__name__ == 'napcat' for func in notify_functions)
    print(f"\nnapcat 推送是否启用: {napcat_enabled}")
    
    return napcat_enabled

def test_napcat_directly():
    """直接测试napcat函数"""
    print("\n=== 直接测试napcat函数 ===")
    
    # 临时设置配置（如果环境变量没设置的话）
    if not notify.push_config.get('NAPCAT_URL'):
        notify.push_config['NAPCAT_URL'] = 'http://192.168.10.252:7765'
        print("临时设置 NAPCAT_URL")
    
    if not notify.push_config.get('NAPCAT_GROUP_ID'):
        notify.push_config['NAPCAT_GROUP_ID'] = '1027740020'
        print("临时设置 NAPCAT_GROUP_ID")
    
    try:
        notify.napcat("测试标题", "这是一条直接调用napcat函数的测试消息")
    except Exception as e:
        print(f"直接调用napcat函数失败: {e}")

def test_send_function():
    """测试send函数"""
    print("\n=== 测试send函数 ===")
    
    # 确保napcat配置存在
    if not notify.push_config.get('NAPCAT_URL'):
        notify.push_config['NAPCAT_URL'] = 'http://192.168.10.252:7765'
    
    if not notify.push_config.get('NAPCAT_GROUP_ID'):
        notify.push_config['NAPCAT_GROUP_ID'] = '1027740020'
    
    try:
        notify.send("测试通知", "这是通过send函数发送的测试消息")
    except Exception as e:
        print(f"send函数调用失败: {e}")

if __name__ == "__main__":
    print("=== napcat 推送调试工具 ===")
    
    # 调试配置
    napcat_enabled = debug_config()
    
    # 如果napcat没有启用，先直接测试napcat函数
    if not napcat_enabled:
        print("\nnapcat未启用，尝试直接测试...")
        test_napcat_directly()
    
    # 测试完整的send函数
    test_send_function()
    
    print("\n=== 调试完成 ===")
