#!/usr/bin/env python3
# _*_ coding:utf-8 _*_

"""
自动QQ推送脚本
读取最新的邮件内容，并发送到QQ群
"""

import os
import sys
import time
import imaplib
import email
from email.header import decode_header
import requests

def napcat_push(title: str, content: str) -> None:
    """napcat 推送函数"""
    napcat_url = os.getenv("NAPCAT_URL", "http://192.168.10.252:7765/send_group_msg")
    napcat_group_id = os.getenv("NAPCAT_GROUP_ID", "1027740020")
    
    if not napcat_url or not napcat_group_id:
        print("napcat 服务的 NAPCAT_URL 或 NAPCAT_GROUP_ID 未设置!!")
        return
    
    print("napcat 服务启动")
    
    try:
        if napcat_url.endswith('/send_group_msg') or napcat_url.endswith('/send_msg'):
            url = napcat_url
        else:
            url = f'{napcat_url}/send_group_msg'
        
        group_id = int(napcat_group_id)
        message = f"{title}\n\n{content}"
        
        response = requests.post(
            url,
            json={"group_id": group_id, "message": message},
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        
        if response.status_code == 200:
            print("napcat QQ群推送成功！")
        else:
            print(f"napcat 推送失败，状态码：{response.status_code}，响应：{response.text}")
    except Exception as e:
        print(f"napcat 推送异常：{str(e)}")

def get_latest_email():
    """获取最新的邮件内容"""
    # 这里需要邮箱配置，但比较复杂
    # 更简单的方法是直接使用固定的内容格式
    pass

def push_sf_result():
    """推送顺丰速运结果"""
    # 由于无法直接获取JavaScript脚本的运行结果
    # 我们可以创建一个通用的推送内容
    
    title = "顺丰速运"
    content = """脚本运行完成
    
请查看邮箱获取详细运行结果，或查看青龙面板日志。

如需查看具体积分、签到等信息，请查收邮件通知。"""
    
    print("=== 自动QQ推送开始 ===")
    napcat_push(title, content)
    print("=== 自动QQ推送完成 ===")

if __name__ == "__main__":
    print("=== 顺丰速运自动QQ推送 ===")
    
    # 等待一段时间，确保顺丰脚本运行完成
    if len(sys.argv) > 1 and sys.argv[1] == "--wait":
        print("等待顺丰脚本运行完成...")
        time.sleep(30)  # 等待30秒
    
    push_sf_result()
