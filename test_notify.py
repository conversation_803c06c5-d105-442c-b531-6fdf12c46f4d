#!/usr/bin/env python3
# _*_ coding:utf-8 _*_

import os
import sys

# 添加当前目录到 Python 路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入通知模块
try:
    from notify import send, push_config
    print("成功导入 notify 模块")
except ImportError:
    try:
        # 如果文件名是 notify(1).py
        import importlib.util
        spec = importlib.util.spec_from_file_location("notify", "notify(1).py")
        notify = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(notify)
        send = notify.send
        push_config = notify.push_config
        print("成功导入 notify(1).py 模块")
    except Exception as e:
        print(f"导入模块失败: {e}")
        sys.exit(1)

def check_config():
    """检查当前配置"""
    print("\n=== 当前推送配置检查 ===")
    
    # 检查 SMTP 配置
    smtp_configs = ['SMTP_SERVER', 'SMTP_SSL', 'SMTP_EMAIL', 'SMTP_PASSWORD', 'SMTP_NAME']
    smtp_configured = all(push_config.get(key) for key in smtp_configs)
    print(f"SMTP 邮件推送: {'✓ 已配置' if smtp_configured else '✗ 未配置'}")
    if smtp_configured:
        print(f"  - SMTP_SERVER: {push_config.get('SMTP_SERVER')}")
        print(f"  - SMTP_EMAIL: {push_config.get('SMTP_EMAIL')}")
    
    # 检查 napcat 配置
    napcat_configured = push_config.get('NAPCAT_URL') and push_config.get('NAPCAT_GROUP_ID')
    print(f"napcat QQ推送: {'✓ 已配置' if napcat_configured else '✗ 未配置'}")
    if napcat_configured:
        print(f"  - NAPCAT_URL: {push_config.get('NAPCAT_URL')}")
        print(f"  - NAPCAT_GROUP_ID: {push_config.get('NAPCAT_GROUP_ID')}")
    
    # 检查其他推送方式
    other_configs = {
        'BARK_PUSH': 'Bark推送',
        'DD_BOT_TOKEN': '钉钉机器人',
        'FSKEY': '飞书机器人',
        'PUSH_KEY': 'Server酱',
        'TG_BOT_TOKEN': 'Telegram机器人',
        'QYWX_KEY': '企业微信机器人'
    }
    
    for key, name in other_configs.items():
        configured = bool(push_config.get(key))
        print(f"{name}: {'✓ 已配置' if configured else '✗ 未配置'}")
    
    print("\n=== 环境变量设置建议 ===")
    if not napcat_configured:
        print("要启用 napcat QQ推送，请设置以下环境变量：")
        print("export NAPCAT_URL='http://192.168.10.252:7765'")
        print("export NAPCAT_GROUP_ID='1027740020'")
        print("\n或在青龙面板中添加这两个环境变量")

def test_push():
    """测试推送功能"""
    print("\n=== 开始测试推送 ===")
    
    title = "测试通知"
    content = "这是一条测试消息，用于验证推送功能是否正常工作。"
    
    try:
        send(title, content)
        print("推送测试完成，请检查各个推送渠道是否收到消息")
    except Exception as e:
        print(f"推送测试失败: {e}")

if __name__ == "__main__":
    print("=== 通知推送配置检查和测试工具 ===")
    
    # 检查配置
    check_config()
    
    # 询问是否进行测试
    print("\n是否要进行推送测试？(y/n): ", end="")
    try:
        choice = input().lower().strip()
        if choice in ['y', 'yes', '是']:
            test_push()
        else:
            print("跳过推送测试")
    except KeyboardInterrupt:
        print("\n测试已取消")
