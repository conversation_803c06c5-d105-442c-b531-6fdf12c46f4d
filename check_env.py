#!/usr/bin/env python3
# _*_ coding:utf-8 _*_

import os

print("=== 检查环境变量 ===")

# 检查napcat相关环境变量
napcat_url = os.getenv("NAPCAT_URL")
napcat_group_id = os.getenv("NAPCAT_GROUP_ID")

print(f"NAPCAT_URL: {napcat_url}")
print(f"NAPCAT_GROUP_ID: {napcat_group_id}")

# 检查SMTP相关环境变量（确认邮箱推送为什么能工作）
smtp_server = os.getenv("SMTP_SERVER")
smtp_email = os.getenv("SMTP_EMAIL")

print(f"SMTP_SERVER: {smtp_server}")
print(f"SMTP_EMAIL: {smtp_email}")

print("\n=== 设置napcat环境变量 ===")
if not napcat_url:
    os.environ["NAPCAT_URL"] = "http://192.168.10.252:7765"
    print("已设置 NAPCAT_URL = http://192.168.10.252:7765")

if not napcat_group_id:
    os.environ["NAPCAT_GROUP_ID"] = "1027740020"
    print("已设置 NAPCAT_GROUP_ID = 1027740020")

print("\n=== 重新检查环境变量 ===")
print(f"NAPCAT_URL: {os.getenv('NAPCAT_URL')}")
print(f"NAPCAT_GROUP_ID: {os.getenv('NAPCAT_GROUP_ID')}")

# 现在测试notify
print("\n=== 测试notify推送 ===")
import importlib.util
spec = importlib.util.spec_from_file_location("notify", "notify(1).py")
notify = importlib.util.module_from_spec(spec)
spec.loader.exec_module(notify)

# 检查push_config
print(f"notify.push_config NAPCAT_URL: {notify.push_config.get('NAPCAT_URL')}")
print(f"notify.push_config NAPCAT_GROUP_ID: {notify.push_config.get('NAPCAT_GROUP_ID')}")

# 检查推送函数列表
notify_functions = notify.add_notify_function()
print(f"\n已注册的推送函数: {[func.__name__ for func in notify_functions]}")

# 测试推送
print("\n=== 开始推送测试 ===")
notify.send("测试通知", "这是一条测试消息，用于验证napcat推送是否正常工作")
