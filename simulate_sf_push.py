#!/usr/bin/env python3
# _*_ coding:utf-8 _*_

import os
import sys

print("=== 模拟顺丰速运脚本推送 ===")

# 检查环境变量
print("环境变量检查:")
print(f"NAPCAT_URL: {os.getenv('NAPCAT_URL')}")
print(f"NAPCAT_GROUP_ID: {os.getenv('NAPCAT_GROUP_ID')}")

# 导入notify模块（模拟顺丰速运脚本的导入方式）
try:
    from notify import send, push_config, add_notify_function
    print("成功导入notify模块")
except ImportError as e:
    print(f"导入notify模块失败: {e}")
    sys.exit(1)

# 检查push_config中的配置
print(f"\npush_config中的配置:")
print(f"NAPCAT_URL: {push_config.get('NAPCAT_URL')}")
print(f"NAPCAT_GROUP_ID: {push_config.get('NAPCAT_GROUP_ID')}")

# 检查推送函数列表
print(f"\n推送函数检查:")
notify_functions = add_notify_function()
function_names = [func.__name__ for func in notify_functions]
print(f"已注册的推送函数: {function_names}")
print(f"napcat是否在列表中: {'napcat' in function_names}")

# 模拟顺丰速运的推送内容
title = "顺丰速运"
content = """-------------- 账号[1] --------------
账号[1][18677170463]登录成功
账号[1][18677170463]超值福利签到失败: 系统繁忙，请稍后再试
账号[1][18677170463]今天已签到
账号[1][18677170463]积分: 55
账号[1][18677170463]可以采蜜冒险0次
账号[1][18677170463]采蜜游戏丰蜜: 395"""

print(f"\n=== 开始推送 ===")
print("============== 推送 ==============")

# 调用推送函数
try:
    send(title, content)
except Exception as e:
    print(f"推送失败: {e}")

print("=== 推送完成 ===")
