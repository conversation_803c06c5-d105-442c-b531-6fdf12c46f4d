#!/usr/bin/env python3
# _*_ coding:utf-8 _*_

"""
SMTP-napcat 桥接脚本
监听 SMTP 邮件推送，并同时触发 napcat QQ 推送
"""

import os
import sys
import time
import threading
import smtplib
import requests
from email.mime.text import MIMEText
from email.header import Header
from email.utils import formataddr

# 原始的 smtplib.SMTP_SSL 和 smtplib.SMTP
_original_smtp_ssl = smtplib.SMTP_SSL
_original_smtp = smtplib.SMTP

# 存储最后一次推送的内容
last_push_data = {"title": "", "content": "", "timestamp": 0}

def napcat_push(title: str, content: str) -> None:
    """
    napcat 推送函数
    """
    napcat_url = os.getenv("NAPCAT_URL", "http://192.168.10.252:7765/send_group_msg")
    napcat_group_id = os.getenv("NAPCAT_GROUP_ID", "1027740020")
    
    if not napcat_url or not napcat_group_id:
        print("napcat 服务的 NAPCAT_URL 或 NAPCAT_GROUP_ID 未设置!!")
        return
    
    print("napcat 服务启动")
    
    try:
        # 构建完整的 URL
        if napcat_url.endswith('/send_group_msg') or napcat_url.endswith('/send_msg'):
            url = napcat_url
        else:
            url = f'{napcat_url}/send_group_msg'
        
        group_id = int(napcat_group_id)
        
        # 构建推送内容（与SMTP邮件格式保持一致）
        message = f"{title}\n\n{content}"
        
        # 发送请求到 napcat
        response = requests.post(
            url,
            json={"group_id": group_id, "message": message},
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        
        # 验证推送结果
        if response.status_code == 200:
            print("napcat QQ群推送成功！")
        else:
            print(f"napcat 推送失败，状态码：{response.status_code}，响应：{response.text}")
    except Exception as e:
        print(f"napcat 推送异常：{str(e)}")

class SMTPWrapper:
    """SMTP 包装类，用于拦截邮件发送"""
    
    def __init__(self, original_class):
        self.original_class = original_class
        self.instance = None
    
    def __call__(self, *args, **kwargs):
        self.instance = self.original_class(*args, **kwargs)
        return SMTPInstance(self.instance)

class SMTPInstance:
    """SMTP 实例包装类"""
    
    def __init__(self, smtp_instance):
        self.smtp_instance = smtp_instance
    
    def __getattr__(self, name):
        return getattr(self.smtp_instance, name)
    
    def sendmail(self, from_addr, to_addrs, msg, *args, **kwargs):
        """拦截 sendmail 方法"""
        global last_push_data
        
        # 调用原始的 sendmail
        result = self.smtp_instance.sendmail(from_addr, to_addrs, msg, *args, **kwargs)
        
        # 解析邮件内容
        try:
            if isinstance(msg, bytes):
                msg_str = msg.decode('utf-8')
            else:
                msg_str = str(msg)
            
            # 提取标题和内容
            title = ""
            content = ""
            
            lines = msg_str.split('\n')
            for line in lines:
                if line.startswith('Subject:'):
                    # 解析标题
                    subject_line = line[8:].strip()
                    if '=?utf-8?' in subject_line:
                        # 解码 UTF-8 编码的标题
                        import email.header
                        decoded = email.header.decode_header(subject_line)
                        title = ''.join([
                            part.decode(encoding or 'utf-8') if isinstance(part, bytes) else part
                            for part, encoding in decoded
                        ])
                    else:
                        title = subject_line
                elif line.strip() and not line.startswith(('From:', 'To:', 'Subject:', 'Content-', 'MIME-')):
                    # 提取内容
                    if content:
                        content += '\n' + line
                    else:
                        content = line
            
            # 清理内容
            content = content.strip()
            
            # 避免重复推送
            current_time = time.time()
            if (last_push_data["title"] != title or 
                last_push_data["content"] != content or 
                current_time - last_push_data["timestamp"] > 5):
                
                last_push_data = {
                    "title": title,
                    "content": content,
                    "timestamp": current_time
                }
                
                # 在新线程中执行 napcat 推送
                def async_napcat_push():
                    time.sleep(1)  # 稍微延迟，确保 SMTP 推送完成
                    napcat_push(title, content)
                
                thread = threading.Thread(target=async_napcat_push)
                thread.daemon = True
                thread.start()
        
        except Exception as e:
            print(f"解析邮件内容失败: {e}")
        
        return result

def install_smtp_hook():
    """安装 SMTP 钩子"""
    smtplib.SMTP_SSL = SMTPWrapper(_original_smtp_ssl)
    smtplib.SMTP = SMTPWrapper(_original_smtp)
    print("SMTP-napcat 桥接已启动，将自动同步推送到 QQ 群")

def uninstall_smtp_hook():
    """卸载 SMTP 钩子"""
    smtplib.SMTP_SSL = _original_smtp_ssl
    smtplib.SMTP = _original_smtp
    print("SMTP-napcat 桥接已停止")

if __name__ == "__main__":
    print("=== SMTP-napcat 桥接脚本 ===")
    print("此脚本需要在其他脚本运行前导入")
    print("使用方法：")
    print("1. 在脚本开头添加：")
    print("   import smtp_napcat_bridge")
    print("   smtp_napcat_bridge.install_smtp_hook()")
    print("2. 或者直接运行：python -c \"import smtp_napcat_bridge; smtp_napcat_bridge.install_smtp_hook(); exec(open('your_script.py').read())\"")
    
    # 测试功能
    install_smtp_hook()
    print("桥接已安装，等待 SMTP 推送...")
    
    try:
        while True:
            time.sleep(10)
    except KeyboardInterrupt:
        uninstall_smtp_hook()
        print("桥接已停止")
