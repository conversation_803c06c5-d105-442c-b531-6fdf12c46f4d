#!/usr/bin/env python3
# _*_ coding:utf-8 _*_

"""
notify包装脚本
用于让JavaScript脚本也能使用napcat推送功能
"""

import sys
import os

# 确保可以导入notify模块
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def main():
    """主函数，处理命令行参数或使用默认测试内容"""

    # 检查是否有命令行参数
    if len(sys.argv) >= 3:
        # 使用命令行参数
        title = sys.argv[1]
        content = sys.argv[2]
        print("使用命令行参数进行推送")
    else:
        # 使用默认测试内容
        title = "测试通知"
        content = "这是一条测试消息，验证napcat推送是否正常工作"
        print("使用默认测试内容进行推送")

    try:
        # 导入notify模块
        from notify import send

        # 调用推送函数
        print("=== 包装脚本推送开始 ===")
        print("============== 推送 ==============")
        send(title, content)
        print("=== 包装脚本推送完成 ===")

    except Exception as e:
        print(f"推送失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
